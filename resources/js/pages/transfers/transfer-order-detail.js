/**
 * Transfer Order Detail Management
 */

'use strict';

$(function () {
    // Submit for approval
    $(document).on('click', '.submit-approval-btn', function () {
        var transferOrderId = $(this).data('id');

        Swal.fire({
            title: '<PERSON><PERSON><PERSON> nhận gửi duyệt',
            text: 'Bạn có chắc chắn muốn gửi phiếu chuyển hàng này để duyệt?',
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'Gửi duyệt',
            cancelButtonText: 'Hủy',
            customClass: {
                confirmButton: 'btn btn-primary me-3',
                cancelButton: 'btn btn-outline-secondary'
            },
            buttonsStyling: false
        }).then(function (result) {
            if (result.isConfirmed) {
                showLoading();

                $.ajax({
                    url: baseUrl + 'admin/transfer-orders/' + transferOrderId + '/submit',
                    type: 'POST',
                    data: {
                        _token: $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function (response) {
                        hideLoading();
                        if (response.success) {
                            Swal.fire({
                                icon: 'success',
                                title: 'Th<PERSON>nh công!',
                                text: response.message,
                                customClass: {
                                    confirmButton: 'btn btn-success'
                                }
                            }).then(function() {
                                location.reload();
                            });
                        }
                    },
                    error: function (xhr) {
                        hideLoading();
                        var response = xhr.responseJSON;
                        Swal.fire({
                            icon: 'error',
                            title: 'Lỗi!',
                            text: response.message || 'Có lỗi xảy ra khi gửi duyệt.',
                            customClass: {
                                confirmButton: 'btn btn-danger'
                            }
                        });
                    }
                });
            }
        });
    });

    // Approve
    $(document).on('click', '.approve-btn', function () {
        var transferOrderId = $(this).data('id');

        Swal.fire({
            title: 'Xác nhận duyệt',
            text: 'Bạn có chắc chắn muốn duyệt phiếu chuyển hàng này?',
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'Duyệt',
            cancelButtonText: 'Hủy',
            customClass: {
                confirmButton: 'btn btn-success me-3',
                cancelButton: 'btn btn-outline-secondary'
            },
            buttonsStyling: false
        }).then(function (result) {
            if (result.isConfirmed) {
                showLoading();

                $.ajax({
                    url: baseUrl + 'admin/transfer-orders/' + transferOrderId + '/approve',
                    type: 'POST',
                    data: {
                        _token: $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function (response) {
                        hideLoading();
                        if (response.success) {
                            Swal.fire({
                                icon: 'success',
                                title: 'Thành công!',
                                text: response.message,
                                customClass: {
                                    confirmButton: 'btn btn-success'
                                }
                            }).then(function() {
                                location.reload();
                            });
                        }
                    },
                    error: function (xhr) {
                        hideLoading();
                        var response = xhr.responseJSON;
                        Swal.fire({
                            icon: 'error',
                            title: 'Lỗi!',
                            text: response.message || 'Có lỗi xảy ra khi duyệt.',
                            customClass: {
                                confirmButton: 'btn btn-danger'
                            }
                        });
                    }
                });
            }
        });
    });

    // Reject
    var rejectTransferOrderId = null;
    $(document).on('click', '.reject-btn', function () {
        rejectTransferOrderId = $(this).data('id');
        $('#rejectModal').modal('show');
    });

    $('#rejectForm').on('submit', function (e) {
        e.preventDefault();

        if (!rejectTransferOrderId) return;

        var reason = $('#reject-reason').val().trim();
        if (!reason) {
            Swal.fire({
                icon: 'error',
                title: 'Lỗi!',
                text: 'Vui lòng nhập lý do từ chối.',
                customClass: {
                    confirmButton: 'btn btn-danger'
                }
            });
            return;
        }

        showLoading();

        $.ajax({
            url: baseUrl + 'admin/transfer-orders/' + rejectTransferOrderId + '/reject',
            type: 'POST',
            data: {
                reason: reason,
                _token: $('meta[name="csrf-token"]').attr('content')
            },
            success: function (response) {
                hideLoading();
                $('#rejectModal').modal('hide');

                if (response.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Thành công!',
                        text: response.message,
                        customClass: {
                            confirmButton: 'btn btn-success'
                        }
                    }).then(function() {
                        location.reload();
                    });
                    $('#reject-reason').val('');
                    rejectTransferOrderId = null;
                }
            },
            error: function (xhr) {
                hideLoading();
                var response = xhr.responseJSON;
                Swal.fire({
                    icon: 'error',
                    title: 'Lỗi!',
                    text: response.message || 'Có lỗi xảy ra khi từ chối.',
                    customClass: {
                        confirmButton: 'btn btn-danger'
                    }
                });
            }
        });
    });

    // Cancel
    $(document).on('click', '.cancel-btn', function () {
        var transferOrderId = $(this).data('id');

        Swal.fire({
            title: 'Xác nhận hủy',
            text: 'Bạn có chắc chắn muốn hủy phiếu chuyển hàng này?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Hủy phiếu',
            cancelButtonText: 'Không',
            customClass: {
                confirmButton: 'btn btn-warning me-3',
                cancelButton: 'btn btn-outline-secondary'
            },
            buttonsStyling: false
        }).then(function (result) {
            if (result.isConfirmed) {
                showLoading();

                $.ajax({
                    url: baseUrl + 'admin/transfer-orders/' + transferOrderId + '/cancel',
                    type: 'POST',
                    data: {
                        _token: $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function (response) {
                        hideLoading();
                        if (response.success) {
                            Swal.fire({
                                icon: 'success',
                                title: 'Thành công!',
                                text: response.message,
                                customClass: {
                                    confirmButton: 'btn btn-success'
                                }
                            }).then(function() {
                                location.reload();
                            });
                        }
                    },
                    error: function (xhr) {
                        hideLoading();
                        var response = xhr.responseJSON;
                        Swal.fire({
                            icon: 'error',
                            title: 'Lỗi!',
                            text: response.message || 'Có lỗi xảy ra khi hủy.',
                            customClass: {
                                confirmButton: 'btn btn-danger'
                            }
                        });
                    }
                });
            }
        });
    });

    // IMEI Management
    var currentItemId = null;
    var currentProductName = null;
    var currentQuantity = null;

    // Manage IMEI button click
    $(document).on('click', '.manage-imei-btn', function () {
        currentItemId = $(this).data('item-id');
        currentProductName = $(this).data('product-name');
        currentQuantity = $(this).data('quantity');

        $('#imei-product-name').text(currentProductName);
        $('#imei-input').val('');

        loadImeiList();
        $('#imeiModal').modal('show');
    });

    // Add IMEI
    function addImei() {
        var imei = $('#imei-input').val().trim();
        if (!imei) {
            Swal.fire({
                icon: 'error',
                title: 'Lỗi!',
                text: 'Vui lòng nhập IMEI.',
                customClass: {
                    confirmButton: 'btn btn-danger'
                }
            });
            return;
        }

        showLoading();

        $.ajax({
            url: baseUrl + 'admin/transfer-orders/' + getTransferOrderId() + '/add-imei',
            type: 'POST',
            data: {
                item_id: currentItemId,
                imei: imei,
                _token: $('meta[name="csrf-token"]').attr('content')
            },
            success: function (response) {
                hideLoading();
                if (response.success) {
                    $('#imei-input').val('');
                    loadImeiList();
                    updateItemStatus();

                    Swal.fire({
                        icon: 'success',
                        title: 'Thành công!',
                        text: response.message,
                        timer: 1500,
                        showConfirmButton: false
                    });
                }
            },
            error: function (xhr) {
                hideLoading();
                var response = xhr.responseJSON;
                Swal.fire({
                    icon: 'error',
                    title: 'Lỗi!',
                    text: response.message || 'Có lỗi xảy ra khi thêm IMEI.',
                    customClass: {
                        confirmButton: 'btn btn-danger'
                    }
                });
            }
        });
    }

    // Load IMEI list
    function loadImeiList() {
        $.ajax({
            url: baseUrl + 'admin/transfer-orders/' + getTransferOrderId() + '/items/' + currentItemId + '/imeis',
            type: 'GET',
            success: function (response) {
                if (response.success) {
                    renderImeiTable(response.data);
                }
            },
            error: function (xhr) {
                console.error('Error loading IMEI list:', xhr);
            }
        });
    }

    // Render IMEI table
    function renderImeiTable(imeis) {
        var tbody = $('#imei-table-body');
        tbody.empty();

        if (imeis.length === 0) {
            tbody.append('<tr><td colspan="5" class="text-center text-muted">Chưa có IMEI nào</td></tr>');
            return;
        }

        imeis.forEach(function (imei, index) {
            var row = `
                <tr>
                    <td>${index + 1}</td>
                    <td>${imei.imei}</td>
                    <td>${imei.scanned_by ? imei.scanned_by.name : '-'}</td>
                    <td>${imei.scanned_at ? new Date(imei.scanned_at).toLocaleString('vi-VN') : '-'}</td>
                    <td>
                        <button type="button" class="btn btn-sm btn-outline-danger remove-imei-btn" data-imei-id="${imei.id}">
                            <i class="ri-delete-bin-line"></i>
                        </button>
                    </td>
                </tr>
            `;
            tbody.append(row);
        });
    }

    // Remove IMEI
    $(document).on('click', '.remove-imei-btn', function () {
        var imeiId = $(this).data('imei-id');

        Swal.fire({
            title: 'Xác nhận xóa',
            text: 'Bạn có chắc chắn muốn xóa IMEI này?',
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'Xóa',
            cancelButtonText: 'Hủy',
            customClass: {
                confirmButton: 'btn btn-danger me-3',
                cancelButton: 'btn btn-outline-secondary'
            },
            buttonsStyling: false
        }).then(function (result) {
            if (result.isConfirmed) {
                showLoading();

                $.ajax({
                    url: baseUrl + 'admin/transfer-orders/' + getTransferOrderId() + '/remove-imei/' + imeiId,
                    type: 'DELETE',
                    data: {
                        _token: $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function (response) {
                        hideLoading();
                        if (response.success) {
                            loadImeiList();
                            updateItemStatus();
                        }
                    },
                    error: function (xhr) {
                        hideLoading();
                        var response = xhr.responseJSON;
                        Swal.fire({
                            icon: 'error',
                            title: 'Lỗi!',
                            text: response.message || 'Có lỗi xảy ra khi xóa IMEI.',
                            customClass: {
                                confirmButton: 'btn btn-danger'
                            }
                        });
                    }
                });
            }
        });
    });

    // IMEI input enter key
    $('#imei-input').on('keypress', function (e) {
        if (e.which === 13) {
            e.preventDefault();
            addImei();
        }
    });

    // Add IMEI button
    $('#add-imei-btn').on('click', function () {
        addImei();
    });

    // Batch Management
    $(document).on('click', '.manage-batch-btn', function () {
        currentItemId = $(this).data('item-id');
        currentProductName = $(this).data('product-name');
        currentQuantity = $(this).data('quantity');

        $('#batch-product-name').text(currentProductName);
        $('#batch-form')[0].reset();

        loadBatchList();
        $('#batchModal').modal('show');
    });

    // Add Batch
    $('#batch-form').on('submit', function (e) {
        e.preventDefault();

        var batchNumber = $('#batch-number').val().trim();
        var quantity = parseFloat($('#batch-quantity').val());
        var expiryDate = $('#batch-expiry').val();

        if (!batchNumber) {
            Swal.fire({
                icon: 'error',
                title: 'Lỗi!',
                text: 'Vui lòng nhập số lô.',
                customClass: {
                    confirmButton: 'btn btn-danger'
                }
            });
            return;
        }

        if (!quantity || quantity <= 0) {
            Swal.fire({
                icon: 'error',
                title: 'Lỗi!',
                text: 'Vui lòng nhập số lượng hợp lệ.',
                customClass: {
                    confirmButton: 'btn btn-danger'
                }
            });
            return;
        }

        showLoading();

        $.ajax({
            url: baseUrl + 'admin/transfer-orders/' + getTransferOrderId() + '/add-batch',
            type: 'POST',
            data: {
                item_id: currentItemId,
                batch_number: batchNumber,
                quantity: quantity,
                expiry_date: expiryDate || null,
                _token: $('meta[name="csrf-token"]').attr('content')
            },
            success: function (response) {
                hideLoading();
                if (response.success) {
                    $('#batch-form')[0].reset();
                    loadBatchList();
                    updateItemStatus();

                    Swal.fire({
                        icon: 'success',
                        title: 'Thành công!',
                        text: response.message,
                        timer: 1500,
                        showConfirmButton: false
                    });
                }
            },
            error: function (xhr) {
                hideLoading();
                var response = xhr.responseJSON;
                Swal.fire({
                    icon: 'error',
                    title: 'Lỗi!',
                    text: response.message || 'Có lỗi xảy ra khi thêm batch.',
                    customClass: {
                        confirmButton: 'btn btn-danger'
                    }
                });
            }
        });
    });

    // Load Batch list
    function loadBatchList() {
        $.ajax({
            url: baseUrl + 'admin/transfer-orders/' + getTransferOrderId() + '/items/' + currentItemId + '/batches',
            type: 'GET',
            success: function (response) {
                if (response.success) {
                    renderBatchTable(response.data);
                }
            },
            error: function (xhr) {
                console.error('Error loading batch list:', xhr);
            }
        });
    }

    // Render Batch table
    function renderBatchTable(batches) {
        var tbody = $('#batch-table-body');
        tbody.empty();

        if (batches.length === 0) {
            tbody.append('<tr><td colspan="5" class="text-center text-muted">Chưa có batch nào</td></tr>');
            return;
        }

        batches.forEach(function (batch, index) {
            var expiryDate = batch.expiry_date ? new Date(batch.expiry_date).toLocaleDateString('vi-VN') : '-';
            var row = `
                <tr>
                    <td>${index + 1}</td>
                    <td>${batch.batch_number}</td>
                    <td>${parseFloat(batch.quantity).toLocaleString('vi-VN')}</td>
                    <td>${expiryDate}</td>
                    <td>
                        <button type="button" class="btn btn-sm btn-outline-danger remove-batch-btn" data-batch-id="${batch.id}">
                            <i class="ri-delete-bin-line"></i>
                        </button>
                    </td>
                </tr>
            `;
            tbody.append(row);
        });
    }

    // Remove Batch
    $(document).on('click', '.remove-batch-btn', function () {
        var batchId = $(this).data('batch-id');

        Swal.fire({
            title: 'Xác nhận xóa',
            text: 'Bạn có chắc chắn muốn xóa batch này?',
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'Xóa',
            cancelButtonText: 'Hủy',
            customClass: {
                confirmButton: 'btn btn-danger me-3',
                cancelButton: 'btn btn-outline-secondary'
            },
            buttonsStyling: false
        }).then(function (result) {
            if (result.isConfirmed) {
                showLoading();

                $.ajax({
                    url: baseUrl + 'admin/transfer-orders/' + getTransferOrderId() + '/remove-batch/' + batchId,
                    type: 'DELETE',
                    data: {
                        _token: $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function (response) {
                        hideLoading();
                        if (response.success) {
                            loadBatchList();
                            updateItemStatus();
                        }
                    },
                    error: function (xhr) {
                        hideLoading();
                        var response = xhr.responseJSON;
                        Swal.fire({
                            icon: 'error',
                            title: 'Lỗi!',
                            text: response.message || 'Có lỗi xảy ra khi xóa batch.',
                            customClass: {
                                confirmButton: 'btn btn-danger'
                            }
                        });
                    }
                });
            }
        });
    });

    // Helper functions
    function getTransferOrderId() {
        var pathParts = window.location.pathname.split('/');
        return pathParts[pathParts.length - 1];
    }

    function updateItemStatus() {
        // Reload page to update item status
        setTimeout(function() {
            location.reload();
        }, 1000);
    }

    // Override submit for approval to check readiness
    var originalSubmitForApproval = window.submitForApproval;
    window.submitForApproval = function(transferOrderId) {
        // Check if transfer order is ready for approval
        $.ajax({
            url: baseUrl + 'admin/transfer-orders/' + transferOrderId + '/check-ready',
            type: 'GET',
            success: function (response) {
                if (response.success && response.is_ready) {
                    // Call original submit function
                    if (originalSubmitForApproval) {
                        originalSubmitForApproval(transferOrderId);
                    }
                } else {
                    Swal.fire({
                        icon: 'warning',
                        title: 'Chưa sẵn sàng!',
                        text: response.message || 'Vui lòng nhập đầy đủ IMEI/batch cho tất cả sản phẩm trước khi gửi duyệt.',
                        customClass: {
                            confirmButton: 'btn btn-warning'
                        }
                    });
                }
            },
            error: function (xhr) {
                console.error('Error checking readiness:', xhr);
                // If check fails, proceed with original function
                if (originalSubmitForApproval) {
                    originalSubmitForApproval(transferOrderId);
                }
            }
        });
    };
});
